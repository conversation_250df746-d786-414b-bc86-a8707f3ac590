<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建模型"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="模型名称" prop="modelName">
        <el-input
          v-model="formData.modelName"
          placeholder="请输入模型名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="告警类型" prop="alarmType">
        <el-select v-model="formData.alarmType" placeholder="请选择告警类型" clearable style="width: 100%">
          <el-option label="类型一" value="1" />
          <el-option label="类型二" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="上传封面图" prop="coverImageFileId">
        <UploadImgDetail
          v-model="coverUrl"
          :file-size="5"
          :file-type="['image/jpeg', 'image/png']"
          width="120px"
          height="120px"
          :upload-api="handleUpload"
          @upload-success="handleUploadSuccess"
        />
      </el-form-item>

      <el-form-item label="上传模型" prop="modelFileId">
        <UploadFile v-model="formData.modelFileId">
          <template #tip>
            <div class="upload-tip">请上传模型文件</div>
          </template>
        </UploadFile>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadImgDetail, UploadFile } from '@/components/UploadFile'
import { createModel } from '@/api/alg/model'
import { uploadFileDetail } from '@/api/infra/file'
import type { CreateModelForm } from '../types'

defineOptions({ name: 'CreateModelDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<CreateModelForm>({
  modelName: '',
  modelFileId: '',
  coverImageFileId: undefined,
  modelVideoFileId: undefined,
  status: undefined,
  alarmType: undefined
})
const coverUrl = ref('')
let coverImageFileId: number | undefined

const formRules: FormRules<CreateModelForm> = {
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '模型名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  modelFileId: [{ required: true, message: '请上传模型文件', trigger: 'blur' }],
  alarmType: [{ required: true, message: '请选择告警类型', trigger: 'change' }],
  coverImageFileId: [{ required: true, message: '请上传封面图', trigger: 'change' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    modelName: '',
    modelFileId: '',
    coverImageFileId: undefined,
    modelVideoFileId: undefined,
    status: undefined,
    alarmType: undefined
  })
  coverUrl.value = ''
  coverImageFileId = undefined
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      ...formData,
      ...(coverImageFileId && { coverImageFileId })
    }

    await createModel(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建模型失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpload = async (file: File) => {
  const res = await uploadFileDetail({ file })
  ElMessage.success('上传成功')
  return res
}

const handleUploadSuccess = (res: any) => {
  coverUrl.value = res.data.url
  coverImageFileId = res.data.id
}

const fillWithTestData = () => {
  formData.modelName = '测试模型 ' + new Date().getTime()
  formData.modelFileId = '12345'
}

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 14px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
